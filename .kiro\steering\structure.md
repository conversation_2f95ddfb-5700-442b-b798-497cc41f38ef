# Project Structure

## Root Directory Organization

```
├── .kiro/                    # Kiro AI assistant configuration
├── .next/                    # Next.js build output
├── .source/                  # Source documentation configuration
├── content/                  # Documentation content (Fumadocs)
├── debug/                    # Development debugging files
├── public/                   # Static assets
├── src/                      # Main source code
├── node_modules/             # Dependencies
└── [config files]           # Various configuration files
```

## Source Directory (`src/`)

### Core Application Structure
- **`src/app/`**: Next.js App Router directory
  - `[locale]/`: Internationalized routes
  - `(legal)/`: Legal pages (privacy, terms)
  - `api/`: API routes
  - `globals.css`: Global styles
  - `theme.css`: Theme configuration
  - `layout.tsx`: Root layout

### Feature Modules
- **`src/components/`**: Reusable UI components
  - `ui/`: shadcn/ui components
  - `analytics/`: Analytics components
  - `blocks/`: Page building blocks
  - `console/`: Admin/dashboard components
  - `dashboard/`: User dashboard components
  - `feedback/`: User feedback components
  - `icon/`: Custom icons
  - `invite/`: User invitation components
  - `locale/`: Language switching components
  - `markdown/`: MDX/Markdown components
  - `sign/`: Authentication components
  - `theme/`: Theme switching components

### Core Services
- **`src/auth/`**: Authentication configuration and utilities
- **`src/db/`**: Database configuration and schema
  - `config.ts`: Drizzle configuration
  - `schema.ts`: Database schema definitions
  - `index.ts`: Database connection
  - `migrations/`: Database migration files

### Utilities & Helpers
- **`src/lib/`**: Shared utilities and configurations
- **`src/hooks/`**: Custom React hooks
- **`src/types/`**: TypeScript type definitions
- **`src/models/`**: Data models and business logic
- **`src/services/`**: External service integrations
- **`src/providers/`**: React context providers
- **`src/contexts/`**: React contexts

### Internationalization
- **`src/i18n/`**: Internationalization setup
  - `messages/`: Translation files by locale
  - `pages/`: Page-specific translations
  - `routing.ts`: Locale routing configuration
  - `locale.ts`: Locale definitions
  - `navigation.ts`: Internationalized navigation
  - `request.ts`: Server-side i18n utilities

### AI Integration
- **`src/aisdk/`**: AI SDK configurations and utilities

## Configuration Files

### Core Configuration
- **`package.json`**: Dependencies and scripts
- **`tsconfig.json`**: TypeScript configuration with path aliases
- **`next.config.mjs`**: Next.js configuration with MDX, i18n, and bundle analyzer
- **`components.json`**: shadcn/ui configuration

### Styling
- **`postcss.config.mjs`**: PostCSS configuration
- **`tailwind.config.ts`**: Tailwind CSS configuration (referenced in components.json)

### Environment & Deployment
- **`.env.example`**: Environment variable template
- **`.env.development`**: Development environment variables
- **`Dockerfile`**: Container configuration
- **`vercel.json`**: Vercel deployment configuration

### Documentation
- **`source.config.ts`**: Fumadocs configuration
- **`.source/`**: Source documentation build configuration

## Path Aliases

The project uses TypeScript path aliases defined in `tsconfig.json`:
- `@/*`: Maps to `./src/*`
- `@/.source`: Maps to `./.source/index.ts`

## Component Organization Patterns

### UI Components
- Use shadcn/ui components from `@/components/ui`
- Custom components organized by feature in `@/components/[feature]`
- Shared utilities in `@/lib/utils`

### Page Structure
- Internationalized routes under `src/app/[locale]/`
- API routes under `src/app/api/`
- Legal pages under `src/app/(legal)/`

### Database Schema
- All schema definitions in `src/db/schema.ts`
- Migrations generated in `src/db/migrations/`
- Database configuration in `src/db/config.ts`

## File Naming Conventions

- **Components**: PascalCase (e.g., `UserProfile.tsx`)
- **Pages**: lowercase with hyphens (e.g., `user-profile/page.tsx`)
- **Utilities**: camelCase (e.g., `formatDate.ts`)
- **Types**: PascalCase with `.types.ts` suffix
- **Hooks**: camelCase starting with `use` (e.g., `useAuth.ts`)
- **API Routes**: lowercase (e.g., `route.ts`)

## Import Patterns

- Use path aliases (`@/`) for internal imports
- Group imports: external libraries first, then internal modules
- Use named exports for utilities and components
- Use default exports for pages and main components