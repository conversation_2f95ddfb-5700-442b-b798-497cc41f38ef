# Product Overview

ShipAny Template One is an AI SaaS startup boilerplate designed to help developers ship AI-powered SaaS applications quickly. The template provides a complete foundation with authentication, payments, internationalization, and AI integrations.

## Key Features

- **Multi-language Support**: Built-in i18n with support for English, Chinese, Japanese, Korean, Russian, French, German, Arabic, Spanish, and Italian
- **Authentication**: NextAuth.js integration with Google and GitHub providers, plus Google One Tap
- **Payment Processing**: Stripe integration for subscription and one-time payments
- **AI Integration**: Multiple AI SDK providers including OpenAI, DeepSeek, Replicate, and OpenRouter
- **Database**: PostgreSQL with Drizzle ORM
- **Analytics**: Support for Google Analytics, OpenPanel, and Plausible
- **Documentation**: Fumadocs integration for MDX-based documentation
- **Cloud Storage**: AWS S3 compatible storage integration

## Target Audience

Developers and entrepreneurs looking to quickly launch AI-powered SaaS applications without building infrastructure from scratch.

## Deployment Options

- Vercel (primary)
- Cloudflare (alternative branch)
- Docker containerization support