# Technology Stack

## Core Framework
- **Next.js 15.2.3**: React framework with App Router
- **React 19**: Latest React with Server Components
- **TypeScript 5.7.2**: Strict typing enabled
- **Tailwind CSS 4.1.4**: Utility-first CSS framework

## Build System & Package Manager
- **pnpm**: Primary package manager
- **Turbopack**: Development bundler (via `--turbopack` flag)
- **PostCSS**: CSS processing
- **Bundle Analyzer**: Optional build analysis

## Database & ORM
- **PostgreSQL**: Primary database
- **Drizzle ORM**: Type-safe database toolkit
- **Drizzle Kit**: Database migrations and studio

## Authentication & Authorization
- **NextAuth.js 5.0.0-beta.25**: Authentication framework
- **Google OAuth**: Social login provider
- **GitHub OAuth**: Social login provider
- **Google One Tap**: Enhanced login experience

## AI & ML Integration
- **Vercel AI SDK**: Core AI framework
- **OpenAI**: GPT models integration
- **DeepSeek**: Alternative AI provider
- **Replicate**: ML model hosting
- **OpenRouter**: AI model routing

## UI Components & Styling
- **Radix UI**: Headless component primitives
- **shadcn/ui**: Pre-built component library
- **Lucide React**: Icon library
- **Framer Motion**: Animation library
- **Class Variance Authority**: Component variant management

## Internationalization
- **next-intl**: i18n framework for Next.js
- **Locale Detection**: Automatic language detection
- **Multi-language Routing**: Locale-based routing

## Payment Processing
- **Stripe**: Payment processing and subscriptions
- **Stripe.js**: Client-side integration

## Analytics & Monitoring
- **Google Analytics**: Web analytics
- **OpenPanel**: Privacy-focused analytics
- **Plausible**: Lightweight analytics alternative

## Documentation
- **Fumadocs**: MDX-based documentation system
- **MDX**: Markdown with JSX components

## Development Tools
- **ESLint**: Code linting with Next.js config
- **cross-env**: Cross-platform environment variables
- **tsx**: TypeScript execution

## Common Commands

### Development
```bash
pnpm dev          # Start development server with Turbopack
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run ESLint
pnpm analyze      # Build with bundle analysis
```

### Database Operations
```bash
pnpm db:generate  # Generate database migrations
pnpm db:migrate   # Run database migrations
pnpm db:studio    # Open Drizzle Studio
pnpm db:push      # Push schema changes directly
```

### Docker
```bash
pnpm docker:build # Build Docker image
```

### Post-install
```bash
pnpm postinstall  # Process MDX files (runs automatically)
```

## Environment Configuration
- `.env.development`: Development environment variables
- `.env.example`: Template for environment setup
- Environment variables cover database, auth, payments, analytics, and storage